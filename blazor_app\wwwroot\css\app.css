/* Professional Design System - Inspired by React Layout */
:root {
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --border-radius: 0.75rem;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --bg-gray-50: #f9fafb;
}

body {
    background-color: var(--bg-gray-50);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
}

/* Card System */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Buttons */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-outline-primary {
    border: 1px solid #3b82f6;
    color: #3b82f6;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-1px);
}

/* Stats Cards */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.stat-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.stat-icon {
    background: var(--primary-gradient);
    border-radius: 0.5rem;
    padding: 0.75rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Navigation */
.navbar {
    background: white !important;
    box-shadow: var(--card-shadow);
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
    font-weight: 700;
    color: var(--text-primary) !important;
    font-size: 1.25rem;
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.nav-link:hover {
    color: #3b82f6 !important;
}

/* Profile Avatar */
.profile-avatar {
    background: var(--primary-gradient);
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Alert System */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

/* Form Elements */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tables */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--bg-gray-50);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
}

/* Footer */
.footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: white;
    border-top: 1px solid var(--border-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
    }
}

/* Arabic RTL Support - Enhanced for comprehensive RTL layout */
.rtl-layout {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arial Unicode MS';
    direction: rtl;
    text-align: right;
}

/* Navigation RTL Support */
.rtl-layout .navbar-nav {
    flex-direction: row-reverse;
}

.rtl-layout .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

.rtl-layout .navbar-toggler {
    margin-left: 0;
    margin-right: auto;
}

/* Margin and Padding RTL Adjustments */
.rtl-layout .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.rtl-layout .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.rtl-layout .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.rtl-layout .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }

.rtl-layout .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.rtl-layout .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.rtl-layout .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }

.rtl-layout .pe-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.rtl-layout .pe-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.rtl-layout .pe-3 { padding-left: 1rem !important; padding-right: 0 !important; }

.rtl-layout .ps-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.rtl-layout .ps-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.rtl-layout .ps-3 { padding-right: 1rem !important; padding-left: 0 !important; }

/* Text Alignment RTL */
.rtl-layout .text-start { text-align: right !important; }
.rtl-layout .text-end { text-align: left !important; }

/* Dropdown RTL Support */
.rtl-layout .dropdown-menu {
    right: 0;
    left: auto;
}

.rtl-layout .dropdown-toggle::after {
    margin-left: 0;
    margin-right: 0.255em;
}

/* Button RTL Support */
.rtl-layout .btn + .btn {
    margin-left: 0.5rem;
    margin-right: 0;
}

.rtl-layout .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--bs-border-radius);
    border-bottom-left-radius: var(--bs-border-radius);
}

.rtl-layout .btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

/* Flex RTL Support */
.rtl-layout .d-flex:not(.flex-column) {
    flex-direction: row-reverse;
}

.rtl-layout .justify-content-start {
    justify-content: flex-end !important;
}

.rtl-layout .justify-content-end {
    justify-content: flex-start !important;
}

.rtl-layout .justify-content-between {
    flex-direction: row-reverse;
}

/* Card RTL Support */
.rtl-layout .card-header .d-flex:not(.flex-column) {
    flex-direction: row-reverse;
}

.rtl-layout .stat-card .d-flex:not(.flex-column) {
    flex-direction: row-reverse;
}

/* Table RTL Support */
.rtl-layout .table th,
.rtl-layout .table td {
    text-align: right;
}

.rtl-layout .table th.text-center,
.rtl-layout .table td.text-center {
    text-align: center !important;
}

/* Form RTL Support */
.rtl-layout .form-check {
    padding-left: 0;
    padding-right: 1.25em;
}

.rtl-layout .form-check-input {
    margin-left: 0;
    margin-right: -1.25em;
    float: right;
}

.rtl-layout .input-group > .form-control:not(:last-child),
.rtl-layout .input-group > .form-select:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--bs-border-radius);
    border-bottom-left-radius: var(--bs-border-radius);
}

.rtl-layout .input-group > .form-control:not(:first-child),
.rtl-layout .input-group > .form-select:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

/* Progress Bar RTL Support */
.rtl-layout .progress-bar {
    direction: ltr; /* Keep progress bars left-to-right for consistency */
}

/* Badge RTL Support */
.rtl-layout .badge {
    direction: ltr; /* Keep badges left-to-right for numbers */
}

/* Alert RTL Support */
.rtl-layout .alert-dismissible .btn-close {
    right: auto;
    left: 0;
}

/* Breadcrumb RTL Support */
.rtl-layout .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
    content: "‹"; /* RTL breadcrumb separator */
}

/* Pagination RTL Support */
.rtl-layout .page-link {
    margin-left: 0;
    margin-right: -1px;
}

.rtl-layout .page-item:first-child .page-link {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

.rtl-layout .page-item:last-child .page-link {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--bs-border-radius);
    border-bottom-left-radius: var(--bs-border-radius);
}

/* Language Switcher Specific */
.language-switcher {
    display: flex;
    align-items: center;
}

.rtl-layout .language-switcher {
    flex-direction: row-reverse;
}

/* Department Tree RTL Support */
.rtl-layout .department-children {
    margin-left: 0;
    margin-right: 1.5rem;
    border-left: none;
    border-right: 2px solid #e9ecef;
    padding-left: 0;
    padding-right: 1rem;
}

/* Step Progress RTL Support */
.rtl-layout .step {
    text-align: right;
}

.rtl-layout .step-connector {
    transform: scaleX(-1);
}

/* Profile Avatar RTL Support */
.rtl-layout .profile-avatar {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Icon RTL Support */
.rtl-layout .fas.fa-arrow-left::before {
    content: "\f061"; /* fa-arrow-right */
}

.rtl-layout .fas.fa-arrow-right::before {
    content: "\f060"; /* fa-arrow-left */
}

/* Custom RTL Utilities */
.rtl-layout .float-start {
    float: right !important;
}

.rtl-layout .float-end {
    float: left !important;
}

/* Page layout */
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

/* Blazor specific styles */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}
